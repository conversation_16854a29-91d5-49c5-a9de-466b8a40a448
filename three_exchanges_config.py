#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三大交易所统一配置文件
支持OKX、Binance、Gate.io的完整配置管理
"""

# 交易所配置
EXCHANGES_CONFIG = {
    'okx': {
        'name': 'OKX',
        'api_key': 'your_okx_api_key',
        'secret_key': 'your_okx_secret_key',
        'passphrase': 'your_okx_passphrase',
        'base_url': 'https://www.okx.com',
        'ws_url': 'wss://ws.okx.com:8443/ws/v5/',
        'testnet': {
            'base_url': 'https://www.okx.com',  # OKX使用相同URL
            'ws_url': 'wss://wspap.okx.com:8443/ws/v5/'
        },
        'rate_limits': {
            'requests_per_second': 20,
            'trading_per_second': 60,
            'burst_limit': 100
        },
        'supported_features': {
            'spot': True,
            'futures': True,
            'options': True,
            'margin': True,
            'lending': True
        }
    },
    'binance': {
        'name': 'Binance',
        'api_key': 'your_binance_api_key',
        'secret_key': 'your_binance_secret_key',
        'base_url': 'https://api.binance.com',
        'futures_url': 'https://fapi.binance.com',
        'margin_url': 'https://api.binance.com',
        'ws_url': 'wss://stream.binance.com:9443/ws/',
        'testnet': {
            'base_url': 'https://testnet.binance.vision',
            'futures_url': 'https://testnet.binancefuture.com',
            'ws_url': 'wss://testnet.binance.vision/ws/'
        },
        'rate_limits': {
            'requests_per_minute': 1200,
            'trading_per_second': 10,
            'weight_limit': 1200
        },
        'supported_features': {
            'spot': True,
            'futures': True,
            'options': False,
            'margin': True,
            'lending': True
        }
    },
    'gate': {
        'name': 'Gate.io',
        'api_key': 'your_gate_api_key',
        'secret_key': 'your_gate_secret_key',
        'base_url': 'https://api.gateio.ws',
        'ws_url': 'wss://api.gateio.ws/ws/v4/',
        'testnet': {
            'base_url': 'https://fx-api-testnet.gateio.ws',
            'ws_url': 'wss://fx-ws-testnet.gateio.ws/v4/ws/'
        },
        'rate_limits': {
            'requests_per_second': 10,
            'trading_per_second': 10,
            'burst_limit': 50
        },
        'supported_features': {
            'spot': True,
            'futures': True,
            'options': False,
            'margin': True,
            'lending': True
        }
    }
}

# 交易配置
TRADING_CONFIG = {
    'default_exchange': 'binance',
    'use_testnet': True,  # 建议先使用测试网
    'default_order_type': 'limit',
    'default_time_in_force': 'GTC',
    'order_timeout': 30,  # 订单超时时间(秒)
    'max_retries': 3,
    'retry_delay': 1,
    'sync_interval': 5,  # 订单同步间隔(秒)
}

# 风险管理配置
RISK_CONFIG = {
    'max_position_size': {
        'BTC': 1.0,
        'ETH': 10.0,
        'default': 100.0
    },
    'max_daily_loss': 1000.0,  # 最大日亏损(USDT)
    'max_drawdown': 0.1,       # 最大回撤(10%)
    'stop_loss_ratio': 0.05,   # 止损比例(5%)
    'take_profit_ratio': 0.1,  # 止盈比例(10%)
    'position_size_ratio': 0.1, # 单次开仓占总资金比例(10%)
    'max_open_orders': 50,     # 最大开放订单数
}

# 策略配置
STRATEGY_CONFIG = {
    'arbitrage': {
        'enabled': True,
        'min_profit_rate': 0.001,  # 最小利润率(0.1%)
        'max_position_size': 1.0,
        'exchanges': ['binance', 'okx'],
        'symbols': ['BTCUSDT', 'ETHUSDT'],
        'check_interval': 1,  # 检查间隔(秒)
    },
    'grid': {
        'enabled': True,
        'base_price': 50000,
        'grid_size': 100,      # 网格间距
        'grid_count': 10,      # 网格数量
        'order_size': 0.001,   # 订单大小
        'profit_ratio': 0.01,  # 网格利润率(1%)
        'symbols': ['BTCUSDT'],
        'exchanges': ['binance']
    },
    'dca': {  # 定投策略
        'enabled': False,
        'interval': 3600,      # 定投间隔(秒)
        'amount': 100,         # 定投金额(USDT)
        'symbols': ['BTCUSDT', 'ETHUSDT'],
        'exchange': 'binance'
    },
    'momentum': {
        'enabled': False,
        'lookback_period': 20,
        'threshold': 0.02,
        'symbols': ['BTCUSDT'],
        'exchange': 'binance'
    }
}

# 交易对映射
SYMBOL_MAPPING = {
    'BTC': {
        'okx': {
            'spot': 'BTC-USDT',
            'futures': 'BTC-USDT-SWAP',
            'options': 'BTC-USD-'
        },
        'binance': {
            'spot': 'BTCUSDT',
            'futures': 'BTCUSDT',
            'margin': 'BTCUSDT'
        },
        'gate': {
            'spot': 'BTC_USDT',
            'futures': 'BTC_USDT',
            'margin': 'BTC_USDT'
        }
    },
    'ETH': {
        'okx': {
            'spot': 'ETH-USDT',
            'futures': 'ETH-USDT-SWAP'
        },
        'binance': {
            'spot': 'ETHUSDT',
            'futures': 'ETHUSDT',
            'margin': 'ETHUSDT'
        },
        'gate': {
            'spot': 'ETH_USDT',
            'futures': 'ETH_USDT',
            'margin': 'ETH_USDT'
        }
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'trading.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'console_output': True
}

# WebSocket配置
WEBSOCKET_CONFIG = {
    'ping_interval': 20,
    'ping_timeout': 10,
    'reconnect_interval': 5,
    'max_reconnect_attempts': 10,
    'auto_reconnect': True
}

# 数据库配置(可选)
DATABASE_CONFIG = {
    'enabled': False,
    'type': 'sqlite',  # sqlite, mysql, postgresql
    'sqlite_file': 'trading.db',
    'mysql': {
        'host': 'localhost',
        'port': 3306,
        'database': 'trading',
        'username': 'root',
        'password': 'password'
    },
    'tables': {
        'orders': 'orders',
        'positions': 'positions',
        'balances': 'balances',
        'trades': 'trades'
    }
}

# 通知配置
NOTIFICATION_CONFIG = {
    'enabled': False,
    'telegram': {
        'bot_token': 'your_telegram_bot_token',
        'chat_id': 'your_telegram_chat_id',
        'notifications': {
            'order_filled': True,
            'position_opened': True,
            'position_closed': True,
            'error_occurred': True,
            'daily_report': True
        }
    },
    'email': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '<EMAIL>',
        'password': 'your_email_password',
        'to_email': '<EMAIL>'
    },
    'webhook': {
        'url': 'https://your-webhook-url.com',
        'secret': 'your_webhook_secret'
    }
}

# 监控配置
MONITORING_CONFIG = {
    'enabled': True,
    'check_interval': 60,  # 监控检查间隔(秒)
    'alerts': {
        'high_latency': 1000,      # 高延迟阈值(毫秒)
        'api_error_rate': 0.1,     # API错误率阈值(10%)
        'balance_change': 0.05,    # 余额变化阈值(5%)
        'position_change': 0.1     # 持仓变化阈值(10%)
    },
    'metrics': {
        'save_to_file': True,
        'file_path': 'metrics.json',
        'retention_days': 30
    }
}

def get_exchange_config(exchange_name: str, use_testnet: bool = False) -> dict:
    """获取交易所配置"""
    if exchange_name not in EXCHANGES_CONFIG:
        raise ValueError(f"不支持的交易所: {exchange_name}")
    
    config = EXCHANGES_CONFIG[exchange_name].copy()
    
    if use_testnet and 'testnet' in config:
        # 合并测试网配置
        testnet_config = config.pop('testnet')
        config.update(testnet_config)
    
    return config

def get_symbol_for_exchange(base_symbol: str, exchange_name: str, market_type: str = 'spot') -> str:
    """获取交易所对应的交易对格式"""
    if base_symbol in SYMBOL_MAPPING:
        exchange_symbols = SYMBOL_MAPPING[base_symbol].get(exchange_name, {})
        return exchange_symbols.get(market_type, base_symbol)
    return base_symbol

def validate_config():
    """验证配置"""
    errors = []
    
    # 检查API密钥
    for exchange_name, config in EXCHANGES_CONFIG.items():
        if config['api_key'] == f'your_{exchange_name}_api_key':
            errors.append(f"请配置{exchange_name}的API密钥")
    
    # 检查风险参数
    if RISK_CONFIG['max_drawdown'] <= 0 or RISK_CONFIG['max_drawdown'] >= 1:
        errors.append("最大回撤应该在0-1之间")
    
    if RISK_CONFIG['position_size_ratio'] <= 0 or RISK_CONFIG['position_size_ratio'] > 1:
        errors.append("仓位比例应该在0-1之间")
    
    # 检查策略配置
    for strategy_name, strategy_config in STRATEGY_CONFIG.items():
        if strategy_config.get('enabled') and not strategy_config.get('symbols'):
            errors.append(f"策略{strategy_name}已启用但未配置交易对")
    
    if errors:
        raise ValueError("配置错误:\n" + "\n".join(errors))
    
    return True

def get_active_strategies() -> list:
    """获取已启用的策略列表"""
    active_strategies = []
    for strategy_name, config in STRATEGY_CONFIG.items():
        if config.get('enabled', False):
            active_strategies.append(strategy_name)
    return active_strategies

def get_supported_exchanges() -> list:
    """获取支持的交易所列表"""
    return list(EXCHANGES_CONFIG.keys())

def get_trading_pairs() -> dict:
    """获取所有配置的交易对"""
    pairs = {}
    for symbol, exchanges in SYMBOL_MAPPING.items():
        pairs[symbol] = {}
        for exchange, markets in exchanges.items():
            pairs[symbol][exchange] = list(markets.values())
    return pairs

if __name__ == '__main__':
    # 验证配置
    try:
        validate_config()
        print("✅ 配置验证通过")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
    
    # 显示配置信息
    print(f"\n支持的交易所: {get_supported_exchanges()}")
    print(f"已启用的策略: {get_active_strategies()}")
    print(f"使用测试网: {TRADING_CONFIG['use_testnet']}")
    print(f"默认交易所: {TRADING_CONFIG['default_exchange']}")
    
    # 显示交易对映射
    print("\n交易对映射:")
    for symbol, exchanges in SYMBOL_MAPPING.items():
        print(f"  {symbol}:")
        for exchange, markets in exchanges.items():
            print(f"    {exchange}: {markets}")
