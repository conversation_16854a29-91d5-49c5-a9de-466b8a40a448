#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三大交易所统一量化交易系统
支持OKX、Binance、Gate.io三大主流交易所
整合了现货、期货、保证金交易功能
"""

import time
import hmac
import hashlib
import base64
import json
import requests
import websocket
import threading
from datetime import datetime
from typing import Dict, List, Optional, Union
import logging
from urllib.parse import urlencode

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaseExchange:
    """交易所基类"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: str = None, testnet: bool = False):
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.testnet = testnet
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'UnifiedTradingSystem/1.0'})
        
    def _sign(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """生成签名 - 子类实现"""
        raise NotImplementedError
        
    def _request(self, method: str, endpoint: str, params: dict = None, data: dict = None) -> dict:
        """发送HTTP请求 - 子类实现"""
        raise NotImplementedError
        
    # 统一交易接口
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, 
                   order_type: str = 'limit', **kwargs) -> dict:
        """统一下单接口"""
        raise NotImplementedError
        
    def cancel_order(self, order_id: str, symbol: str) -> dict:
        """统一撤单接口"""
        raise NotImplementedError
        
    def get_order(self, order_id: str, symbol: str) -> dict:
        """查询订单"""
        raise NotImplementedError
        
    def get_orders(self, symbol: str, status: str = 'open') -> List[dict]:
        """查询订单列表"""
        raise NotImplementedError
        
    def get_positions(self) -> List[dict]:
        """获取持仓"""
        raise NotImplementedError
        
    def get_balance(self) -> dict:
        """获取账户余额"""
        raise NotImplementedError

class OKXExchange(BaseExchange):
    """OKX交易所API封装"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: str, testnet: bool = False):
        super().__init__(api_key, secret_key, passphrase, testnet)
        self.base_url = 'https://www.okx.com' if not testnet else 'https://www.okx.com'
        self.ws_url = 'wss://ws.okx.com:8443/ws/v5/private'
        
    def _sign(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """OKX签名算法"""
        message = timestamp + method + request_path + body
        signature = base64.b64encode(
            hmac.new(self.secret_key.encode(), message.encode(), hashlib.sha256).digest()
        ).decode()
        return signature
        
    def _request(self, method: str, endpoint: str, params: dict = None, data: dict = None) -> dict:
        """发送HTTP请求"""
        url = self.base_url + endpoint
        timestamp = datetime.utcnow().isoformat()[:-3] + 'Z'
        
        body = ''
        if data:
            body = json.dumps(data)
            
        signature = self._sign(timestamp, method, endpoint, body)
        
        headers = {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }
        
        try:
            if method == 'GET':
                response = self.session.get(url, headers=headers, params=params)
            else:
                response = self.session.post(url, headers=headers, json=data)
                
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') != '0':
                raise Exception(f"OKX API错误: {result.get('msg', 'Unknown error')}")
                
            return result
        except Exception as e:
            logger.error(f"OKX API请求失败: {e}")
            raise
            
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, 
                   order_type: str = 'limit', **kwargs) -> dict:
        """下单"""
        data = {
            'instId': symbol,
            'tdMode': kwargs.get('td_mode', 'cross'),
            'side': side,
            'ordType': order_type,
            'sz': str(amount),
            'posSide': kwargs.get('pos_side', 'net')
        }
        
        if price and order_type == 'limit':
            data['px'] = str(price)
            
        if kwargs.get('reduce_only'):
            data['reduceOnly'] = True
            
        result = self._request('POST', '/api/v5/trade/order', data=data)
        return self._normalize_order(result['data'][0])
        
    def cancel_order(self, order_id: str, symbol: str) -> dict:
        """撤单"""
        data = {
            'instId': symbol,
            'ordId': order_id
        }
        result = self._request('POST', '/api/v5/trade/cancel-order', data=data)
        return result['data'][0]
        
    def get_order(self, order_id: str, symbol: str) -> dict:
        """查询订单"""
        params = {
            'instId': symbol,
            'ordId': order_id
        }
        result = self._request('GET', '/api/v5/trade/order', params=params)
        return self._normalize_order(result['data'][0])
        
    def get_orders(self, symbol: str, status: str = 'open') -> List[dict]:
        """查询订单列表"""
        params = {
            'instId': symbol,
            'state': 'live' if status == 'open' else 'history'
        }
        result = self._request('GET', '/api/v5/trade/orders-pending' if status == 'open' else '/api/v5/trade/orders-history', params=params)
        return [self._normalize_order(order) for order in result['data']]
        
    def get_positions(self) -> List[dict]:
        """获取持仓"""
        result = self._request('GET', '/api/v5/account/positions')
        return [self._normalize_position(pos) for pos in result['data']]
        
    def get_balance(self) -> dict:
        """获取账户余额"""
        result = self._request('GET', '/api/v5/account/balance')
        return self._normalize_balance(result['data'][0])
        
    def _normalize_order(self, order: dict) -> dict:
        """标准化订单格式"""
        return {
            'order_id': order['ordId'],
            'symbol': order['instId'],
            'side': order['side'],
            'amount': float(order['sz']),
            'price': float(order['px']) if order['px'] else None,
            'filled_amount': float(order['accFillSz']),
            'status': self._map_order_status(order['state']),
            'timestamp': int(order['cTime']),
            'raw': order
        }
        
    def _normalize_position(self, position: dict) -> dict:
        """标准化持仓格式"""
        return {
            'symbol': position['instId'],
            'side': position['posSide'],
            'size': float(position['pos']),
            'entry_price': float(position['avgPx']) if position['avgPx'] else 0,
            'mark_price': float(position['markPx']) if position['markPx'] else 0,
            'pnl': float(position['upl']),
            'margin': float(position['margin']),
            'raw': position
        }
        
    def _normalize_balance(self, balance: dict) -> dict:
        """标准化余额格式"""
        balances = {}
        for detail in balance['details']:
            balances[detail['ccy']] = {
                'free': float(detail['availBal']),
                'used': float(detail['frozenBal']),
                'total': float(detail['bal'])
            }
        return balances
        
    def _map_order_status(self, status: str) -> str:
        """映射订单状态"""
        status_map = {
            'live': 'open',
            'partially_filled': 'partially_filled',
            'filled': 'filled',
            'canceled': 'cancelled'
        }
        return status_map.get(status, status)

class BinanceExchange(BaseExchange):
    """Binance交易所API封装"""
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = False):
        super().__init__(api_key, secret_key, testnet=testnet)
        if testnet:
            self.base_url = 'https://testnet.binance.vision'
            self.futures_url = 'https://testnet.binancefuture.com'
        else:
            self.base_url = 'https://api.binance.com'
            self.futures_url = 'https://fapi.binance.com'
        self.ws_url = 'wss://stream.binance.com:9443/ws/'
        
    def _sign(self, query_string: str) -> str:
        """Binance签名算法"""
        return hmac.new(
            self.secret_key.encode(), 
            query_string.encode(), 
            hashlib.sha256
        ).hexdigest()
        
    def _request(self, method: str, endpoint: str, params: dict = None, data: dict = None, 
                signed: bool = False, futures: bool = False) -> dict:
        """发送HTTP请求"""
        base_url = self.futures_url if futures else self.base_url
        url = base_url + endpoint
        
        if params is None:
            params = {}
        if data:
            params.update(data)
            
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = urlencode(params)
            params['signature'] = self._sign(query_string)
            
        headers = {
            'X-MBX-APIKEY': self.api_key
        }
        
        try:
            if method == 'GET':
                response = self.session.get(url, headers=headers, params=params)
            elif method == 'POST':
                response = self.session.post(url, headers=headers, params=params)
            elif method == 'DELETE':
                response = self.session.delete(url, headers=headers, params=params)
                
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Binance API请求失败: {e}")
            raise
            
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, 
                   order_type: str = 'limit', **kwargs) -> dict:
        """下单"""
        params = {
            'symbol': symbol.replace('-', '').replace('_', ''),
            'side': side.upper(),
            'type': order_type.upper(),
            'quantity': amount
        }
        
        if price and order_type.upper() == 'LIMIT':
            params['price'] = price
            params['timeInForce'] = kwargs.get('time_in_force', 'GTC')
            
        futures = kwargs.get('futures', False)
        if futures:
            if kwargs.get('position_side'):
                params['positionSide'] = kwargs['position_side'].upper()
            if kwargs.get('reduce_only'):
                params['reduceOnly'] = 'true'
                
        endpoint = '/fapi/v1/order' if futures else '/api/v3/order'
        result = self._request('POST', endpoint, data=params, signed=True, futures=futures)
        return self._normalize_order(result, futures)
        
    def cancel_order(self, order_id: str, symbol: str, **kwargs) -> dict:
        """撤单"""
        params = {
            'symbol': symbol.replace('-', '').replace('_', ''),
            'orderId': order_id
        }
        
        futures = kwargs.get('futures', False)
        endpoint = '/fapi/v1/order' if futures else '/api/v3/order'
        result = self._request('DELETE', endpoint, params=params, signed=True, futures=futures)
        return result
        
    def get_order(self, order_id: str, symbol: str, **kwargs) -> dict:
        """查询订单"""
        params = {
            'symbol': symbol.replace('-', '').replace('_', ''),
            'orderId': order_id
        }
        
        futures = kwargs.get('futures', False)
        endpoint = '/fapi/v1/order' if futures else '/api/v3/order'
        result = self._request('GET', endpoint, params=params, signed=True, futures=futures)
        return self._normalize_order(result, futures)
        
    def get_orders(self, symbol: str, status: str = 'open', **kwargs) -> List[dict]:
        """查询订单列表"""
        params = {
            'symbol': symbol.replace('-', '').replace('_', '')
        }
        
        futures = kwargs.get('futures', False)
        if status == 'open':
            endpoint = '/fapi/v1/openOrders' if futures else '/api/v3/openOrders'
        else:
            endpoint = '/fapi/v1/allOrders' if futures else '/api/v3/allOrders'
            
        result = self._request('GET', endpoint, params=params, signed=True, futures=futures)
        return [self._normalize_order(order, futures) for order in result]
        
    def get_positions(self) -> List[dict]:
        """获取持仓"""
        result = self._request('GET', '/fapi/v2/positionRisk', signed=True, futures=True)
        return [self._normalize_position(pos) for pos in result if float(pos['positionAmt']) != 0]
        
    def get_balance(self, **kwargs) -> dict:
        """获取账户余额"""
        futures = kwargs.get('futures', False)
        if futures:
            result = self._request('GET', '/fapi/v2/account', signed=True, futures=True)
            return self._normalize_futures_balance(result)
        else:
            result = self._request('GET', '/api/v3/account', signed=True)
            return self._normalize_spot_balance(result)
            
    def _normalize_order(self, order: dict, futures: bool = False) -> dict:
        """标准化订单格式"""
        return {
            'order_id': str(order['orderId']),
            'symbol': order['symbol'],
            'side': order['side'].lower(),
            'amount': float(order['origQty']),
            'price': float(order['price']) if order['price'] != '0' else None,
            'filled_amount': float(order['executedQty']),
            'status': self._map_order_status(order['status']),
            'timestamp': order['time'],
            'raw': order
        }
        
    def _normalize_position(self, position: dict) -> dict:
        """标准化持仓格式"""
        return {
            'symbol': position['symbol'],
            'side': 'long' if float(position['positionAmt']) > 0 else 'short',
            'size': abs(float(position['positionAmt'])),
            'entry_price': float(position['entryPrice']),
            'mark_price': float(position['markPrice']),
            'pnl': float(position['unRealizedProfit']),
            'margin': float(position['isolatedMargin']),
            'raw': position
        }
        
    def _normalize_spot_balance(self, balance: dict) -> dict:
        """标准化现货余额格式"""
        balances = {}
        for asset in balance['balances']:
            if float(asset['free']) > 0 or float(asset['locked']) > 0:
                balances[asset['asset']] = {
                    'free': float(asset['free']),
                    'used': float(asset['locked']),
                    'total': float(asset['free']) + float(asset['locked'])
                }
        return balances
        
    def _normalize_futures_balance(self, balance: dict) -> dict:
        """标准化期货余额格式"""
        balances = {}
        for asset in balance['assets']:
            if float(asset['walletBalance']) > 0:
                balances[asset['asset']] = {
                    'free': float(asset['availableBalance']),
                    'used': float(asset['walletBalance']) - float(asset['availableBalance']),
                    'total': float(asset['walletBalance'])
                }
        return balances
        
    def _map_order_status(self, status: str) -> str:
        """映射订单状态"""
        status_map = {
            'NEW': 'open',
            'PARTIALLY_FILLED': 'partially_filled',
            'FILLED': 'filled',
            'CANCELED': 'cancelled',
            'REJECTED': 'rejected',
            'EXPIRED': 'expired'
        }
        return status_map.get(status, status.lower())

class GateExchange(BaseExchange):
    """Gate.io交易所API封装"""
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = False):
        super().__init__(api_key, secret_key, testnet=testnet)
        if testnet:
            self.base_url = 'https://fx-api-testnet.gateio.ws'
        else:
            self.base_url = 'https://api.gateio.ws'
        self.ws_url = 'wss://api.gateio.ws/ws/v4/'
        
    def _sign(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """Gate.io签名算法"""
        query_string = ''
        if '?' in request_path:
            request_path, query_string = request_path.split('?', 1)
            
        body_hash = hashlib.sha512(body.encode()).hexdigest()
        message = f"{method}\n{request_path}\n{query_string}\n{body_hash}\n{timestamp}"
        signature = hmac.new(self.secret_key.encode(), message.encode(), hashlib.sha512).hexdigest()
        return signature
        
    def _request(self, method: str, endpoint: str, params: dict = None, data: dict = None) -> dict:
        """发送HTTP请求"""
        url = self.base_url + endpoint
        timestamp = str(int(time.time()))
        
        body = ''
        if data:
            body = json.dumps(data)
            
        # 构建完整的请求路径
        full_path = endpoint
        if params:
            query_string = urlencode(params)
            full_path += '?' + query_string
            
        signature = self._sign(timestamp, method, full_path, body)
        
        headers = {
            'KEY': self.api_key,
            'SIGN': signature,
            'Timestamp': timestamp,
            'Content-Type': 'application/json'
        }
        
        try:
            if method == 'GET':
                response = self.session.get(url, headers=headers, params=params)
            elif method == 'POST':
                response = self.session.post(url, headers=headers, json=data)
            elif method == 'DELETE':
                response = self.session.delete(url, headers=headers, params=params)
                
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Gate.io API请求失败: {e}")
            raise
            
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, 
                   order_type: str = 'limit', **kwargs) -> dict:
        """下单"""
        futures = kwargs.get('futures', False)
        
        if futures:
            # 期货下单
            data = {
                'contract': symbol,
                'size': int(amount) if side == 'buy' else -int(amount),
                'tif': kwargs.get('time_in_force', 'gtc')
            }
            
            if price and order_type == 'limit':
                data['price'] = str(price)
            else:
                data['price'] = '0'  # 市价单
                
            if kwargs.get('reduce_only'):
                data['reduce_only'] = True
                
            endpoint = '/api/v4/futures/usdt/orders'
        else:
            # 现货下单
            data = {
                'currency_pair': symbol,
                'side': side,
                'type': order_type,
                'amount': str(amount)
            }
            
            if price and order_type == 'limit':
                data['price'] = str(price)
                
            endpoint = '/api/v4/spot/orders'
            
        result = self._request('POST', endpoint, data=data)
        return self._normalize_order(result, futures)
        
    def cancel_order(self, order_id: str, symbol: str, **kwargs) -> dict:
        """撤单"""
        futures = kwargs.get('futures', False)
        
        if futures:
            endpoint = f'/api/v4/futures/usdt/orders/{order_id}'
        else:
            endpoint = f'/api/v4/spot/orders/{order_id}'
            
        result = self._request('DELETE', endpoint)
        return result
        
    def get_order(self, order_id: str, symbol: str, **kwargs) -> dict:
        """查询订单"""
        futures = kwargs.get('futures', False)
        
        if futures:
            endpoint = f'/api/v4/futures/usdt/orders/{order_id}'
        else:
            endpoint = f'/api/v4/spot/orders/{order_id}'
            
        result = self._request('GET', endpoint)
        return self._normalize_order(result, futures)
        
    def get_orders(self, symbol: str, status: str = 'open', **kwargs) -> List[dict]:
        """查询订单列表"""
        futures = kwargs.get('futures', False)
        
        params = {}
        if futures:
            params['contract'] = symbol
            params['status'] = 'open' if status == 'open' else 'finished'
            endpoint = '/api/v4/futures/usdt/orders'
        else:
            params['currency_pair'] = symbol
            params['status'] = status
            endpoint = '/api/v4/spot/orders'
            
        result = self._request('GET', endpoint, params=params)
        return [self._normalize_order(order, futures) for order in result]
        
    def get_positions(self) -> List[dict]:
        """获取持仓"""
        result = self._request('GET', '/api/v4/futures/usdt/positions')
        return [self._normalize_position(pos) for pos in result if float(pos['size']) != 0]
        
    def get_balance(self, **kwargs) -> dict:
        """获取账户余额"""
        futures = kwargs.get('futures', False)
        
        if futures:
            result = self._request('GET', '/api/v4/futures/usdt/accounts')
            return self._normalize_futures_balance(result)
        else:
            result = self._request('GET', '/api/v4/spot/accounts')
            return self._normalize_spot_balance(result)
            
    def _normalize_order(self, order: dict, futures: bool = False) -> dict:
        """标准化订单格式"""
        if futures:
            return {
                'order_id': str(order['id']),
                'symbol': order['contract'],
                'side': 'buy' if int(order['size']) > 0 else 'sell',
                'amount': abs(int(order['size'])),
                'price': float(order['price']) if order['price'] != '0' else None,
                'filled_amount': abs(int(order['size']) - int(order['left'])),
                'status': self._map_order_status(order['status']),
                'timestamp': int(order['create_time']),
                'raw': order
            }
        else:
            return {
                'order_id': order['id'],
                'symbol': order['currency_pair'],
                'side': order['side'],
                'amount': float(order['amount']),
                'price': float(order['price']) if order['price'] else None,
                'filled_amount': float(order['filled_total']),
                'status': self._map_order_status(order['status']),
                'timestamp': int(order['create_time']),
                'raw': order
            }
            
    def _normalize_position(self, position: dict) -> dict:
        """标准化持仓格式"""
        size = float(position['size'])
        return {
            'symbol': position['contract'],
            'side': 'long' if size > 0 else 'short',
            'size': abs(size),
            'entry_price': float(position['entry_price']),
            'mark_price': float(position['mark_price']),
            'pnl': float(position['unrealised_pnl']),
            'margin': float(position['margin']),
            'raw': position
        }
        
    def _normalize_spot_balance(self, balances: list) -> dict:
        """标准化现货余额格式"""
        result = {}
        for balance in balances:
            if float(balance['available']) > 0 or float(balance['locked']) > 0:
                result[balance['currency']] = {
                    'free': float(balance['available']),
                    'used': float(balance['locked']),
                    'total': float(balance['available']) + float(balance['locked'])
                }
        return result
        
    def _normalize_futures_balance(self, balance: dict) -> dict:
        """标准化期货余额格式"""
        return {
            'USDT': {
                'free': float(balance['available']),
                'used': float(balance['total']) - float(balance['available']),
                'total': float(balance['total'])
            }
        }
        
    def _map_order_status(self, status: str) -> str:
        """映射订单状态"""
        status_map = {
            'open': 'open',
            'closed': 'filled',
            'cancelled': 'cancelled'
        }
        return status_map.get(status, status)

class UnifiedTradingManager:
    """统一交易管理器"""

    def __init__(self):
        self.exchanges = {}
        self.active_orders = {}
        self.positions = {}
        self.balances = {}

    def add_exchange(self, name: str, exchange: BaseExchange):
        """添加交易所"""
        self.exchanges[name] = exchange
        logger.info(f"添加交易所: {name}")

    def place_order(self, exchange_name: str, symbol: str, side: str, amount: float,
                   price: float = None, order_type: str = 'limit', **kwargs) -> dict:
        """统一下单接口"""
        if exchange_name not in self.exchanges:
            raise ValueError(f"交易所 {exchange_name} 未配置")

        exchange = self.exchanges[exchange_name]

        # 标准化交易对格式
        normalized_symbol = self._normalize_symbol(symbol, exchange_name)

        try:
            result = exchange.place_order(normalized_symbol, side, amount, price, order_type, **kwargs)

            # 记录活跃订单
            order_key = f"{exchange_name}:{result['order_id']}"
            self.active_orders[order_key] = {
                'exchange': exchange_name,
                'order': result
            }

            logger.info(f"下单成功: {exchange_name} {symbol} {side} {amount} @ {price}")
            return result

        except Exception as e:
            logger.error(f"下单失败: {exchange_name} {symbol} - {e}")
            raise

    def cancel_order(self, exchange_name: str, order_id: str, symbol: str, **kwargs) -> dict:
        """统一撤单接口"""
        if exchange_name not in self.exchanges:
            raise ValueError(f"交易所 {exchange_name} 未配置")

        exchange = self.exchanges[exchange_name]
        normalized_symbol = self._normalize_symbol(symbol, exchange_name)

        try:
            result = exchange.cancel_order(order_id, normalized_symbol, **kwargs)

            # 移除活跃订单记录
            order_key = f"{exchange_name}:{order_id}"
            if order_key in self.active_orders:
                del self.active_orders[order_key]

            logger.info(f"撤单成功: {exchange_name} {order_id}")
            return result

        except Exception as e:
            logger.error(f"撤单失败: {exchange_name} {order_id} - {e}")
            raise

    def get_all_positions(self) -> Dict[str, List[dict]]:
        """获取所有交易所持仓"""
        all_positions = {}

        for name, exchange in self.exchanges.items():
            try:
                positions = exchange.get_positions()
                all_positions[name] = positions
                self.positions[name] = positions
            except Exception as e:
                logger.error(f"获取 {name} 持仓失败: {e}")
                all_positions[name] = []

        return all_positions

    def get_all_balances(self) -> Dict[str, dict]:
        """获取所有交易所余额"""
        all_balances = {}

        for name, exchange in self.exchanges.items():
            try:
                balance = exchange.get_balance()
                all_balances[name] = balance
                self.balances[name] = balance
            except Exception as e:
                logger.error(f"获取 {name} 余额失败: {e}")
                all_balances[name] = {}

        return all_balances

    def sync_orders(self):
        """同步订单状态"""
        for order_key, order_info in list(self.active_orders.items()):
            exchange_name = order_info['exchange']
            order = order_info['order']

            try:
                exchange = self.exchanges[exchange_name]
                updated_order = exchange.get_order(order['order_id'], order['symbol'])

                # 更新订单状态
                self.active_orders[order_key]['order'] = updated_order

                # 如果订单已完成，移除活跃订单记录
                if updated_order['status'] in ['filled', 'cancelled', 'rejected']:
                    del self.active_orders[order_key]

            except Exception as e:
                logger.error(f"同步订单状态失败: {order_key} - {e}")

    def _normalize_symbol(self, symbol: str, exchange_name: str) -> str:
        """标准化交易对格式"""
        # 移除所有分隔符，统一格式
        clean_symbol = symbol.replace('-', '').replace('_', '').replace('/', '')

        if exchange_name == 'okx':
            # OKX格式: BTC-USDT-SWAP, BTC-USDT
            if 'SWAP' in symbol.upper():
                return f"{clean_symbol[:3]}-{clean_symbol[3:]}-SWAP"
            else:
                return f"{clean_symbol[:3]}-{clean_symbol[3:]}"
        elif exchange_name == 'binance':
            # Binance格式: BTCUSDT
            return clean_symbol
        elif exchange_name == 'gate':
            # Gate.io格式: BTC_USDT
            return f"{clean_symbol[:3]}_{clean_symbol[3:]}"
        else:
            return symbol

class ArbitrageStrategy:
    """套利策略"""

    def __init__(self, manager: UnifiedTradingManager, symbol: str, exchanges: List[str]):
        self.manager = manager
        self.symbol = symbol
        self.exchanges = exchanges
        self.min_profit_rate = 0.001  # 最小利润率 0.1%
        self.max_position_size = 1.0  # 最大仓位大小

    def check_arbitrage_opportunity(self) -> Optional[dict]:
        """检查套利机会"""
        prices = {}

        # 获取各交易所价格
        for exchange_name in self.exchanges:
            try:
                exchange = self.manager.exchanges[exchange_name]
                # 这里应该获取实时价格，简化示例
                # prices[exchange_name] = exchange.get_ticker(self.symbol)['price']
                pass
            except Exception as e:
                logger.error(f"获取 {exchange_name} 价格失败: {e}")

        if len(prices) < 2:
            return None

        # 找出最高价和最低价
        max_price_exchange = max(prices, key=prices.get)
        min_price_exchange = min(prices, key=prices.get)

        max_price = prices[max_price_exchange]
        min_price = prices[min_price_exchange]

        profit_rate = (max_price - min_price) / min_price

        if profit_rate > self.min_profit_rate:
            return {
                'buy_exchange': min_price_exchange,
                'sell_exchange': max_price_exchange,
                'buy_price': min_price,
                'sell_price': max_price,
                'profit_rate': profit_rate,
                'size': self.max_position_size
            }

        return None

    def execute_arbitrage(self, opportunity: dict):
        """执行套利"""
        try:
            # 同时在两个交易所下单
            buy_order = self.manager.place_order(
                opportunity['buy_exchange'],
                self.symbol,
                'buy',
                opportunity['size'],
                opportunity['buy_price']
            )

            sell_order = self.manager.place_order(
                opportunity['sell_exchange'],
                self.symbol,
                'sell',
                opportunity['size'],
                opportunity['sell_price']
            )

            logger.info(f"套利订单已下达: 买入 {buy_order['order_id']}, 卖出 {sell_order['order_id']}")

            return {
                'buy_order': buy_order,
                'sell_order': sell_order
            }

        except Exception as e:
            logger.error(f"执行套利失败: {e}")
            raise

class GridTradingStrategy:
    """网格交易策略"""

    def __init__(self, manager: UnifiedTradingManager, exchange_name: str, symbol: str,
                 base_price: float, grid_size: float, grid_count: int, order_size: float):
        self.manager = manager
        self.exchange_name = exchange_name
        self.symbol = symbol
        self.base_price = base_price
        self.grid_size = grid_size
        self.grid_count = grid_count
        self.order_size = order_size
        self.grid_orders = {}

    def initialize_grid(self):
        """初始化网格"""
        logger.info(f"初始化网格交易: {self.symbol} 基准价格: {self.base_price}")

        # 创建买入网格
        for i in range(1, self.grid_count + 1):
            buy_price = self.base_price - (i * self.grid_size)
            try:
                order = self.manager.place_order(
                    self.exchange_name,
                    self.symbol,
                    'buy',
                    self.order_size,
                    buy_price
                )
                self.grid_orders[f"buy_{i}"] = order
                logger.info(f"网格买单: 价格 {buy_price}, 订单ID {order['order_id']}")
            except Exception as e:
                logger.error(f"创建网格买单失败: {e}")

        # 创建卖出网格
        for i in range(1, self.grid_count + 1):
            sell_price = self.base_price + (i * self.grid_size)
            try:
                order = self.manager.place_order(
                    self.exchange_name,
                    self.symbol,
                    'sell',
                    self.order_size,
                    sell_price
                )
                self.grid_orders[f"sell_{i}"] = order
                logger.info(f"网格卖单: 价格 {sell_price}, 订单ID {order['order_id']}")
            except Exception as e:
                logger.error(f"创建网格卖单失败: {e}")

    def check_and_refill_grid(self):
        """检查并补充网格"""
        # 检查已成交的订单并重新下单
        for grid_key, order in list(self.grid_orders.items()):
            try:
                updated_order = self.manager.exchanges[self.exchange_name].get_order(
                    order['order_id'], order['symbol']
                )

                if updated_order['status'] == 'filled':
                    logger.info(f"网格订单已成交: {grid_key}")

                    # 重新下单
                    if 'buy' in grid_key:
                        # 买单成交，在更高价格下卖单
                        new_price = order['price'] + self.grid_size
                        new_order = self.manager.place_order(
                            self.exchange_name,
                            self.symbol,
                            'sell',
                            self.order_size,
                            new_price
                        )
                    else:
                        # 卖单成交，在更低价格下买单
                        new_price = order['price'] - self.grid_size
                        new_order = self.manager.place_order(
                            self.exchange_name,
                            self.symbol,
                            'buy',
                            self.order_size,
                            new_price
                        )

                    self.grid_orders[grid_key] = new_order
                    logger.info(f"网格订单已补充: {grid_key} 新价格 {new_price}")

            except Exception as e:
                logger.error(f"检查网格订单失败: {grid_key} - {e}")

def create_trading_system():
    """创建交易系统示例"""
    # 创建统一交易管理器
    manager = UnifiedTradingManager()

    # 配置交易所 (需要替换为实际的API密钥)
    okx_config = {
        'api_key': 'your_okx_api_key',
        'secret_key': 'your_okx_secret_key',
        'passphrase': 'your_okx_passphrase'
    }

    binance_config = {
        'api_key': 'your_binance_api_key',
        'secret_key': 'your_binance_secret_key'
    }

    gate_config = {
        'api_key': 'your_gate_api_key',
        'secret_key': 'your_gate_secret_key'
    }

    # 添加交易所 (取消注释以启用)
    # manager.add_exchange('okx', OKXExchange(**okx_config))
    # manager.add_exchange('binance', BinanceExchange(**binance_config))
    # manager.add_exchange('gate', GateExchange(**gate_config))

    return manager

def demo_trading_operations():
    """演示交易操作"""
    print("=== 交易操作演示 ===")

    # 创建交易系统
    manager = create_trading_system()

    # 示例交易对
    symbol = 'BTCUSDT'

    print(f"交易对: {symbol}")
    print("注意: 以下为演示代码，需要配置真实API密钥才能执行")

    # 下单示例
    print("\n1. 下单示例:")
    print(f"manager.place_order('binance', '{symbol}', 'buy', 0.001, 50000)")

    # 撤单示例
    print("\n2. 撤单示例:")
    print("manager.cancel_order('binance', 'order_id', 'BTCUSDT')")

    # 查询持仓示例
    print("\n3. 查询持仓:")
    print("positions = manager.get_all_positions()")

    # 查询余额示例
    print("\n4. 查询余额:")
    print("balances = manager.get_all_balances()")

def demo_arbitrage_strategy():
    """演示套利策略"""
    print("\n=== 套利策略演示 ===")

    manager = create_trading_system()
    symbol = 'BTCUSDT'
    exchanges = ['binance', 'okx']

    print(f"套利交易对: {symbol}")
    print(f"套利交易所: {exchanges}")

    # 创建套利策略
    arbitrage = ArbitrageStrategy(manager, symbol, exchanges)

    print("\n套利策略配置:")
    print(f"最小利润率: {arbitrage.min_profit_rate * 100}%")
    print(f"最大仓位: {arbitrage.max_position_size}")

    print("\n执行流程:")
    print("1. 检查套利机会: arbitrage.check_arbitrage_opportunity()")
    print("2. 执行套利: arbitrage.execute_arbitrage(opportunity)")

def demo_grid_strategy():
    """演示网格策略"""
    print("\n=== 网格策略演示 ===")

    manager = create_trading_system()

    # 网格策略参数
    exchange_name = 'binance'
    symbol = 'BTCUSDT'
    base_price = 50000
    grid_size = 100
    grid_count = 10
    order_size = 0.001

    print(f"交易所: {exchange_name}")
    print(f"交易对: {symbol}")
    print(f"基准价格: {base_price}")
    print(f"网格间距: {grid_size}")
    print(f"网格数量: {grid_count}")
    print(f"订单大小: {order_size}")

    # 创建网格策略
    grid = GridTradingStrategy(
        manager, exchange_name, symbol,
        base_price, grid_size, grid_count, order_size
    )

    print("\n执行流程:")
    print("1. 初始化网格: grid.initialize_grid()")
    print("2. 检查并补充网格: grid.check_and_refill_grid()")

def main():
    """主函数"""
    print("三大交易所统一量化交易系统")
    print("=" * 50)

    print("支持的交易所:")
    print("✅ OKX - 现货、期货、期权")
    print("✅ Binance - 现货、期货、保证金")
    print("✅ Gate.io - 现货、期货、保证金")

    print("\n核心功能:")
    print("🔄 统一交易接口")
    print("📊 实时持仓监控")
    print("💰 余额管理")
    print("🤖 套利策略")
    print("📈 网格交易")
    print("🔄 订单同步")

    # 演示各种功能
    demo_trading_operations()
    demo_arbitrage_strategy()
    demo_grid_strategy()

    print("\n" + "=" * 50)
    print("使用说明:")
    print("1. 在create_trading_system()函数中配置您的API密钥")
    print("2. 取消注释相应的交易所初始化代码")
    print("3. 根据需要调整策略参数")
    print("4. 建议先在测试网环境中运行")

    print("\n⚠️  风险提示:")
    print("- 量化交易存在风险，请谨慎操作")
    print("- 建议充分测试后再投入实际资金")
    print("- 请确保API密钥安全")
    print("- 注意各交易所的限频规则")

if __name__ == '__main__':
    main()
