#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三大交易所统一量化交易系统使用示例
演示如何使用系统进行各种交易操作和策略执行
"""

import time
import logging
import asyncio
from unified_trading_system import (
    OKXExchange, BinanceExchange, GateExchange, 
    UnifiedTradingManager, ArbitrageStrategy, GridTradingStrategy
)
from three_exchanges_config import (
    get_exchange_config, TRADING_CONFIG, RISK_CONFIG, 
    STRATEGY_CONFIG, get_symbol_for_exchange, validate_config
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TradingBot:
    """三大交易所统一交易机器人"""
    
    def __init__(self):
        self.manager = UnifiedTradingManager()
        self.strategies = {}
        self.running = False
        
    def initialize(self):
        """初始化交易机器人"""
        logger.info("初始化三大交易所统一交易机器人")
        
        # 验证配置
        try:
            validate_config()
            logger.info("配置验证通过")
        except ValueError as e:
            logger.error(f"配置验证失败: {e}")
            return False
        
        # 初始化交易所
        self._initialize_exchanges()
        
        # 初始化策略
        self._initialize_strategies()
        
        logger.info("交易机器人初始化完成")
        return True
        
    def _initialize_exchanges(self):
        """初始化交易所"""
        exchanges_to_init = ['okx', 'binance', 'gate']
        
        for exchange_name in exchanges_to_init:
            try:
                config = get_exchange_config(exchange_name, TRADING_CONFIG['use_testnet'])
                
                # 检查是否配置了API密钥
                if config['api_key'] == f'your_{exchange_name}_api_key':
                    logger.warning(f"跳过 {exchange_name}: 未配置API密钥")
                    continue
                
                # 创建交易所实例
                if exchange_name == 'okx':
                    exchange = OKXExchange(
                        api_key=config['api_key'],
                        secret_key=config['secret_key'],
                        passphrase=config['passphrase'],
                        testnet=TRADING_CONFIG['use_testnet']
                    )
                elif exchange_name == 'binance':
                    exchange = BinanceExchange(
                        api_key=config['api_key'],
                        secret_key=config['secret_key'],
                        testnet=TRADING_CONFIG['use_testnet']
                    )
                elif exchange_name == 'gate':
                    exchange = GateExchange(
                        api_key=config['api_key'],
                        secret_key=config['secret_key'],
                        testnet=TRADING_CONFIG['use_testnet']
                    )
                
                self.manager.add_exchange(exchange_name, exchange)
                logger.info(f"成功初始化交易所: {exchange_name}")
                
            except Exception as e:
                logger.error(f"初始化交易所 {exchange_name} 失败: {e}")
                
    def _initialize_strategies(self):
        """初始化策略"""
        # 套利策略
        if STRATEGY_CONFIG['arbitrage']['enabled']:
            for symbol in STRATEGY_CONFIG['arbitrage']['symbols']:
                strategy = ArbitrageStrategy(
                    self.manager,
                    symbol,
                    STRATEGY_CONFIG['arbitrage']['exchanges']
                )
                strategy.min_profit_rate = STRATEGY_CONFIG['arbitrage']['min_profit_rate']
                strategy.max_position_size = STRATEGY_CONFIG['arbitrage']['max_position_size']
                
                self.strategies[f'arbitrage_{symbol}'] = strategy
                logger.info(f"初始化套利策略: {symbol}")
        
        # 网格策略
        if STRATEGY_CONFIG['grid']['enabled']:
            for symbol in STRATEGY_CONFIG['grid']['symbols']:
                for exchange_name in STRATEGY_CONFIG['grid']['exchanges']:
                    strategy = GridTradingStrategy(
                        self.manager,
                        exchange_name,
                        symbol,
                        STRATEGY_CONFIG['grid']['base_price'],
                        STRATEGY_CONFIG['grid']['grid_size'],
                        STRATEGY_CONFIG['grid']['grid_count'],
                        STRATEGY_CONFIG['grid']['order_size']
                    )
                    
                    self.strategies[f'grid_{exchange_name}_{symbol}'] = strategy
                    logger.info(f"初始化网格策略: {exchange_name} {symbol}")
                    
    def start(self):
        """启动交易机器人"""
        if not self.manager.exchanges:
            logger.error("没有可用的交易所，请检查API配置")
            return
            
        self.running = True
        logger.info("交易机器人启动")
        
        # 启动监控线程
        import threading
        monitor_thread = threading.Thread(target=self._monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
    def stop(self):
        """停止交易机器人"""
        self.running = False
        logger.info("交易机器人停止")
        
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 同步订单状态
                self.manager.sync_orders()
                
                # 更新持仓和余额
                self.manager.get_all_positions()
                self.manager.get_all_balances()
                
                # 执行策略检查
                self._run_strategies()
                
                time.sleep(TRADING_CONFIG['sync_interval'])
                
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(10)
                
    def _run_strategies(self):
        """运行策略"""
        for strategy_name, strategy in self.strategies.items():
            try:
                if isinstance(strategy, ArbitrageStrategy):
                    opportunity = strategy.check_arbitrage_opportunity()
                    if opportunity:
                        logger.info(f"发现套利机会: {opportunity}")
                        # strategy.execute_arbitrage(opportunity)  # 取消注释以执行
                        
                elif isinstance(strategy, GridTradingStrategy):
                    strategy.check_and_refill_grid()
                    
            except Exception as e:
                logger.error(f"策略 {strategy_name} 执行错误: {e}")
                
    def manual_trading_demo(self):
        """手动交易演示"""
        logger.info("开始手动交易演示")
        
        if not self.manager.exchanges:
            logger.warning("没有可用的交易所")
            return
            
        # 选择第一个可用的交易所
        exchange_name = list(self.manager.exchanges.keys())[0]
        symbol = 'BTCUSDT'
        
        try:
            # 1. 查看余额
            balances = self.manager.get_all_balances()
            logger.info(f"账户余额: {balances}")
            
            # 2. 查看持仓
            positions = self.manager.get_all_positions()
            logger.info(f"当前持仓: {positions}")
            
            # 3. 下单演示 (注释掉以避免实际交易)
            # order = self.manager.place_order(
            #     exchange_name, symbol, 'buy', 0.001, 50000
            # )
            # logger.info(f"下单结果: {order}")
            
            logger.info("手动交易演示完成")
            
        except Exception as e:
            logger.error(f"手动交易演示失败: {e}")
            
    def strategy_demo(self):
        """策略演示"""
        logger.info("开始策略演示")
        
        # 套利策略演示
        if 'arbitrage_BTCUSDT' in self.strategies:
            strategy = self.strategies['arbitrage_BTCUSDT']
            logger.info("套利策略演示:")
            logger.info(f"  交易对: {strategy.symbol}")
            logger.info(f"  交易所: {strategy.exchanges}")
            logger.info(f"  最小利润率: {strategy.min_profit_rate * 100}%")
            
        # 网格策略演示
        grid_strategies = [k for k in self.strategies.keys() if k.startswith('grid_')]
        if grid_strategies:
            strategy_name = grid_strategies[0]
            strategy = self.strategies[strategy_name]
            logger.info("网格策略演示:")
            logger.info(f"  策略名称: {strategy_name}")
            logger.info(f"  交易对: {strategy.symbol}")
            logger.info(f"  基准价格: {strategy.base_price}")
            logger.info(f"  网格间距: {strategy.grid_size}")
            logger.info(f"  网格数量: {strategy.grid_count}")
            
            # 初始化网格 (注释掉以避免实际交易)
            # strategy.initialize_grid()
            
    def risk_management_demo(self):
        """风险管理演示"""
        logger.info("风险管理演示")
        
        # 显示风险配置
        logger.info(f"最大日亏损: {RISK_CONFIG['max_daily_loss']} USDT")
        logger.info(f"最大回撤: {RISK_CONFIG['max_drawdown'] * 100}%")
        logger.info(f"止损比例: {RISK_CONFIG['stop_loss_ratio'] * 100}%")
        logger.info(f"止盈比例: {RISK_CONFIG['take_profit_ratio'] * 100}%")
        
        # 检查持仓风险
        positions = self.manager.get_all_positions()
        for exchange_name, exchange_positions in positions.items():
            total_exposure = 0
            for position in exchange_positions:
                exposure = position['size'] * position['mark_price']
                total_exposure += exposure
                logger.info(f"{exchange_name} {position['symbol']}: 风险敞口 ${exposure:.2f}")
            
            logger.info(f"{exchange_name} 总风险敞口: ${total_exposure:.2f}")

def main():
    """主函数"""
    print("三大交易所统一量化交易系统 - 使用示例")
    print("=" * 60)
    
    # 创建交易机器人
    bot = TradingBot()
    
    # 初始化
    if not bot.initialize():
        print("❌ 初始化失败，请检查配置")
        return
    
    print("✅ 初始化成功")
    
    try:
        # 启动机器人
        bot.start()
        
        # 运行演示
        print("\n📊 手动交易演示")
        bot.manual_trading_demo()
        
        print("\n🤖 策略演示")
        bot.strategy_demo()
        
        print("\n🛡️ 风险管理演示")
        bot.risk_management_demo()
        
        print("\n✅ 演示完成")
        
        # 如果有配置的交易所，可以保持运行
        if bot.manager.exchanges:
            print("\n🔄 系统正在运行中...")
            print("按 Ctrl+C 停止系统")
            
            try:
                while True:
                    time.sleep(10)
                    # 显示系统状态
                    active_orders = len(bot.manager.active_orders)
                    print(f"活跃订单数: {active_orders}")
                    
            except KeyboardInterrupt:
                print("\n⏹️ 收到停止信号")
        
    except Exception as e:
        logger.error(f"运行错误: {e}")
        
    finally:
        # 停止机器人
        bot.stop()
        print("🛑 系统已停止")

if __name__ == '__main__':
    main()
